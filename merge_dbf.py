"""
DBF文件合并专家工具（完整增强版）
功能：多文件合并 + 中文字段拆分 + 资源监控 + 安全中断
环境：Python 3.8+ 需安装 pip install dbfread pandas pyarrow psutil tk tqdm
"""

import os
import gc
import time
import pandas as pd
import psutil
from dbfread import DBF, DBFNotFound
from tkinter import Tk, filedialog, messagebox
from collections import defaultdict
from typing import Generator, Dict, Set
from tqdm import tqdm

# =================== 配置参数 ===================
MEMORY_SAFE_LIMIT = 0.7     # 内存使用安全阈值（总内存占比）
INITIAL_CHUNK_SIZE = 2000   # 初始分块行数（根据内存动态调整）
SPARSE_THRESHOLD = 0.01     # 稀疏列筛选阈值（出现频率低于1%忽略）
ENCODING_CANDIDATES = ['gb18030', 'utf_8', 'gbk']  # 中文编码优先级
SPLIT_FIELD = "站点地址"      # 需要拆分的字段名称（中文）
# ================================================

class DBFMergerPro:
    class MemoryMonitor:
        """实时内存监控器"""
        def __init__(self):
            self._process = psutil.Process()
            self.total_mem = psutil.virtual_memory().total / (1024**3)  # 总内存(GB)
            
        @property
        def usage(self) -> float:
            """当前进程内存占用(GB)"""
            return self._process.memory_info().rss / (1024**3)
        
        @property
        def safe_limit(self) -> float:
            """计算实际内存警戒线"""
            return MEMORY_SAFE_LIMIT * self.total_mem

    def __init__(self):
        self.input_files = []       # 输入文件列表
        self.output_path = ""       # 输出路径
        self.active_columns = set() # 需要生成的拆分列
        self.total_records = 0      # 总记录数
        self.processed = 0          # 已处理记录数
        self.start_time = 0         # 处理开始时间
        self.mem_monitor = self.MemoryMonitor()
        self.is_running = True      # 运行状态标志位

    # ----------------- GUI界面操作 -----------------
    def select_files(self) -> bool:
        """多文件选择对话框"""
        root = Tk()
        root.withdraw()
        try:
            # 选择多个DBF文件
            self.input_files = filedialog.askopenfilenames(
                title="选择要合并的DBF文件",
                filetypes=[("DBF Files", "*.dbf")],
                initialdir=os.getcwd()
            )
            if not self.input_files:
                return False
            
            # 选择输出路径
            self.output_path = filedialog.asksaveasfilename(
                title="保存合并结果",
                defaultextension=".parquet",
                filetypes=[
                    ("Parquet文件", "*.parquet"),
                    ("CSV文件 (Excel兼容)", "*.csv")
                ]
            )
            return bool(self.output_path)
        finally:
            root.destroy()

    # ----------------- 核心处理流程 -----------------
    def preprocess(self):
        """预处理：扫描所有文件结构并收集拆分元素"""
        print("\n🔍 开始预处理扫描...")
        column_freq = defaultdict(int)
        total_files = len(self.input_files)
        
        # 第一轮扫描：分析字段结构
        with tqdm(self.input_files, desc="扫描文件结构") as pbar:
            for idx, file in enumerate(pbar, 1):
                try:
                    dbf = self._safe_open_dbf(file)
                    for i, record in enumerate(dbf):
                        # 采样前1000行分析字段内容
                        if i >= 1000: break
                        if value := record.get(SPLIT_FIELD, ""):
                            parsed_fields = self._recursive_split(value)
                            for field_name in parsed_fields.keys():
                                column_freq[field_name] += 1
                    pbar.set_postfix_str(f"{os.path.basename(file)} [文件{idx}/{total_files}]")
                except Exception as e:
                    print(f"\n⚠️ 跳过问题文件 {os.path.basename(file)} 原因: {str(e)}")
                    self.input_files.remove(file)
        
        # 动态筛选有效列
        min_count = max(1, int(len(self.input_files) * 1000 * SPARSE_THRESHOLD))
        self.active_columns = {k for k, v in column_freq.items() if v >= min_count}
        print(f"✅ 识别到有效拆分项 {len(self.active_columns)} 个")

        # 第二轮扫描：计算总行数
        print("\n📊 正在计算总数据量...")
        with tqdm(self.input_files, desc="统计记录数") as pbar:
            self.total_records = sum(
                sum(1 for _ in self._safe_open_dbf(file)) 
                for file in pbar
            )
        print(f"📂 总计需要处理 {self.total_records} 条记录")

    def process_files(self):
        """主处理流程"""
        self.start_time = time.time()
        chunk_size = INITIAL_CHUNK_SIZE  # 动态分块大小
        
        try:
            with tqdm(total=self.total_records, desc="总进度", unit='rec') as main_bar:
                for file in self.input_files:
                    if not self.is_running: break
                    
                    # 单个文件处理流程
                    file_name = os.path.basename(file)
                    dbf = self._safe_open_dbf(file)
                    file_records = sum(1 for _ in dbf)
                    dbf = self._safe_open_dbf(file)  # 重新打开
                    
                    # 文件级进度条
                    with tqdm(total=file_records, desc=f"处理 {file_name}", leave=False) as file_bar:
                        chunk = []
                        for record in dbf:
                            if not self.is_running: break
                            
                            # 处理单条记录
                            processed = self._process_record(record)
                            chunk.append(processed)
                            
                            # 分块写入
                            if len(chunk) >= chunk_size:
                                self._write_chunk(chunk, main_bar, file_bar)
                                chunk_size = self._adjust_chunk(chunk_size)
                                chunk = []
                                self._print_status(file_name)
                                
                        # 写入剩余数据
                        if chunk and self.is_running:
                            self._write_chunk(chunk, main_bar, file_bar)
                            self._print_status(file_name)
        except Exception as e:
            print(f"\n❌ 处理异常终止: {str(e)}")
        finally:
            if self.processed > 0:
                print(f"\n💾 已成功处理 {self.processed} 条记录")
            else:
                print("\n⛔ 未处理任何有效数据")

    # ----------------- 核心工具方法 -----------------
    def _recursive_split(self, value: str) -> Dict[str, str]:
        """
        智能解析站点地址字段，返回字段名到值的映射

        解析规则：
        1. 判断站点地址是否为空，为空则无需处理
        2. 提取设备类型：第一个分号前的内容（如MA、MI、PC、BK、MobileMA等）
        3. 解析键值对：用分号分隔的key=value格式（如IIP=************）
        4. 处理特殊符号：值中的"-"、"^"、"@"、"("等不进行拆分
        5. 保留其他未处理的内容到"其他站点信息"字段
        """
        parsed_fields = {}
        other_info_parts = []  # 收集未处理的部分

        try:
            # 1. 判断站点地址是否为空
            if not value or str(value).strip() == "":
                return parsed_fields

            value_str = str(value).strip()

            # 2. 按分号分割所有部分
            parts = value_str.split(';')

            # 3. 处理第一个部分作为设备类型
            if parts:
                first_part = parts[0].strip()
                if first_part:
                    # 检查第一个部分是否包含等号（如果包含等号，说明没有单独的设备类型）
                    if '=' not in first_part:
                        parsed_fields["设备类型"] = first_part
                        # 从parts中移除已处理的第一个部分
                        parts = parts[1:]
                    else:
                        # 第一个部分就是键值对，没有单独的设备类型
                        pass

            # 4. 处理剩余的键值对部分
            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # 检查是否为键值对格式
                if '=' in part:
                    # 只在第一个等号处分割，避免值中的等号被错误分割
                    key, val = part.split('=', 1)
                    key = key.strip()
                    val = val.strip()

                    if key and val:
                        # 不对值进行进一步拆分，保持完整性
                        # 例如：IDFV=CE9E4EFC-9A3E-49CB-BE6B-0152036521E2 保持完整
                        parsed_fields[key] = val
                    else:
                        # 键或值为空的情况，加入其他信息
                        other_info_parts.append(part)
                else:
                    # 不是键值对格式的部分，加入其他信息
                    other_info_parts.append(part)

            # 5. 处理其他未被提取的信息
            if other_info_parts:
                parsed_fields["其他站点信息"] = ';'.join(other_info_parts)

        except UnicodeDecodeError:  # 处理二进制编码问题
            try:
                decoded = value.encode('raw_unicode_escape').decode('gb18030', errors='replace')
                return self._recursive_split(decoded)
            except:
                parsed_fields["其他站点信息"] = str(value)
        except Exception as e:
            print(f"⚠️ 解析站点地址时出错: {str(e)}, 原始值: {value}")
            # 出错时将原始值保存到其他站点信息
            parsed_fields["其他站点信息"] = str(value)

        return parsed_fields

    def _safe_open_dbf(self, file_path: str) -> DBF:
        """安全打开DBF文件（自动检测编码）"""
        for encoding in ENCODING_CANDIDATES:
            try:
                dbf = DBF(
                    file_path,
                    encoding=encoding,
                    ignore_missing_memofile=True,
                    char_decode_errors='replace'
                )
                if SPLIT_FIELD in dbf.field_names:
                    return dbf
            except (UnicodeDecodeError, DBFNotFound) as e:
                continue
        raise ValueError(f"无法解码文件 {os.path.basename(file_path)}")

    def _process_record(self, record: dict) -> dict:
        """处理单条记录"""
        # 保留原始字段（除拆分字段外）
        row = {k: v for k, v in record.items() if k != SPLIT_FIELD}
        # 处理拆分字段
        if value := record.get(SPLIT_FIELD, ""):
            parsed_fields = self._recursive_split(value)
            # 将解析出的字段添加到行数据中
            for field_name, field_value in parsed_fields.items():
                if field_name in self.active_columns:
                    row[field_name] = field_value
        return row

    def _write_chunk(self, chunk: list, main_bar: tqdm, file_bar: tqdm):
        """安全写入数据块"""
        df = pd.DataFrame(chunk)

        # 补全缺失列，所有拆分字段都设置为空字符串
        missing_cols = self.active_columns - set(df.columns)
        for col in missing_cols:
            df[col] = ""

        # 选择存储格式
        if self.output_path.endswith('.parquet'):
            self._write_parquet(df)
        else:
            self._write_csv(df)

        # 更新进度
        chunk_len = len(chunk)
        main_bar.update(chunk_len)
        file_bar.update(chunk_len)
        self.processed += chunk_len

        # 主动内存回收
        del df, chunk
        gc.collect()

    def _is_boolean_field(self, field_name: str) -> bool:
        """判断字段是否应该为布尔类型"""
        # 新的解析逻辑中，所有字段都是字符串类型，不需要布尔类型
        # 保留此方法以兼容现有代码结构，但始终返回False
        return False

    def _adjust_chunk(self, current_size: int) -> int:
        """动态调整分块大小"""
        if self.mem_monitor.usage > self.mem_monitor.safe_limit:
            new_size = max(500, int(current_size * 0.6))  # 内存超限时缩小分块
            print(f"\n⚠️ 内存告警 ({self.mem_monitor.usage:.1f}GB)，分块调整为 {new_size}")
            return new_size
        return min(current_size * 2, INITIAL_CHUNK_SIZE * 8)  # 安全时扩大分块

    def _print_status(self, filename: str):
        """打印实时状态"""
        elapsed = time.time() - self.start_time
        speed = self.processed / (elapsed + 1e-5)
        status = (
            f"\n【状态更新 {time.strftime('%H:%M:%S')}】"
            f"\n当前文件：{filename}"
            f"\n处理进度：{self.processed:,}/{self.total_records:,} ({self.processed/self.total_records:.1%})"
            f"\n运行时长：{int(elapsed//3600)}h {int(elapsed%3600//60)}m {int(elapsed%60)}s"
            f"\n处理速度：{speed:,.0f} 行/秒"
            f"\n内存使用：{self.mem_monitor.usage:.1f}GB / {self.mem_monitor.total_mem:.1f}GB"
        )
        print(status)

    # ----------------- 数据保存方法 -----------------
    def _write_parquet(self, df: pd.DataFrame):
        """写入Parquet格式"""
        df.to_parquet(
            self.output_path,
            engine='pyarrow',
            compression='zstd',
            index=False,
            existing_data='append' if os.path.exists(self.output_path) else 'overwrite'
        )

    def _write_csv(self, df: pd.DataFrame):
        """写入CSV格式（Excel兼容）"""
        # 只对布尔类型的拆分字段列转换为boolean类型
        df_copy = df.copy()
        for col in self.active_columns:
            if col in df_copy.columns and self._is_boolean_field(col):
                df_copy[col] = df_copy[col].astype('boolean')

        header = not os.path.exists(self.output_path)
        df_copy.to_csv(
            self.output_path,
            mode='a' if not header else 'w',
            header=header,
            index=False,
            encoding='utf_8_sig'
        )

    # ----------------- 主程序入口 -----------------
    def run(self):
        try:
            # 选择文件路径
            if not self.select_files():
                print("⛔ 未选择任何文件，程序退出")
                return
            
            # 预处理及核心处理
            self.preprocess()
            print("\n🚀 启动处理引擎 (按 Ctrl+C 安全中断)...")
            self.process_files()
            
            # 完成提示
            time_cost = time.time() - self.start_time
            print(f"\n✅ 处理完成！耗时 {time_cost//3600:.0f}h {time_cost%3600//60:.0f}m")
            print(f"输出文件保存在：{self.output_path}")
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户主动中断！正在保存已处理数据...")
            self.is_running = False
        except Exception as e:
            print(f"\n❌ 发生未预期错误：{str(e)}")
            if os.path.exists(self.output_path):
                os.remove(self.output_path)
                print("已清理不完整输出文件")

if __name__ == "__main__":
    DBFMergerPro().run()